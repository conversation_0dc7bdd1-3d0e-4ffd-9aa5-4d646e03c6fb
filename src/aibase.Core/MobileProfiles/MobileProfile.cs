using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using aibase.ProjectEntity;

namespace aibase.MobileProfiles;

public class MobileProfile : Entity<int>, IHasCreationTime, IHasModificationTime
{
    [Required]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    [Required]
    public bool IsActive { get; set; }
    
    [Required]
    public bool IsStandard { get; set; }
    
    [Required]
    public bool IsWet { get; set; }
    
    [Required]
    public bool IsDry { get; set; }
    
    [Required]
    public bool IsUv { get; set; }
    
    [Required]
    public bool IsRig { get; set; }
    
    [Required]
    public CameraType MobileCameraType { get; set; }
    
    [Required]
    public CameraType ExternalCameraType { get; set; }
    
    public bool? IsDepthIncrement { get; set; }
    public double? DepthIncrement { get; set; }
    public bool? IsApplyDepthIncrement { get; set; }
    public int? RotateImgMobile { get; set; }
    public int? RotateImgExternal { get; set; }
    
    [Required]
    public int TenantId { get; set; }
    
    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
    
    public ICollection<Project> Projects { get; set; } = [];
}

public enum CameraType
{
    Standard = 1,
    Rig
}