using Abp.Application.Services.Dto;

namespace aibase.RockTree.Dto;

/// <inheritdoc />
public class PagedRockTreeRootResultRequestDto : PagedResultRequestDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? GeologySuiteId { get; set; }
}