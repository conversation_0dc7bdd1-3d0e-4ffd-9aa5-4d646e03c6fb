using Abp.Application.Services.Dto;

namespace aibase.RockTree.Dto;

/// <inheritdoc />
public class PagedRockNodeResultRequestDto : PagedResultRequestDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public RockNodeType? NodeType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? RockTypeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ParentNodeId { get; set; } // To get children of a specific node

    /// <summary>
    /// 
    /// </summary>
    public int? RockTreeRootId { get; set; } // To filter by a specific tree
}