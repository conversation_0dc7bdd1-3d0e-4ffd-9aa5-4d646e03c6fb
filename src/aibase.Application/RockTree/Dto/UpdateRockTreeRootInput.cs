using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;
using Abp.AutoMapper;

namespace aibase.RockTree.Dto;

/// <inheritdoc />
[AutoMapTo(typeof(RockTreeRoot))]
public class UpdateRockTreeRootInput : EntityDto<int>
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? GeologySuiteId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }
}