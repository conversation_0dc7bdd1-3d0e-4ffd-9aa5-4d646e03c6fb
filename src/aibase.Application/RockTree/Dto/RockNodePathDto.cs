using System.Collections.Generic;

namespace aibase.RockTree.Dto;

/// <summary>
/// 
/// </summary>
public class RockNodePathItemDto
{
    /// <summary>
    /// 
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public RockNodeType NodeType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }
}

/// <summary>
/// 
/// </summary>
public class RockNodePathDto
{
    /// <summary>
    /// 
    /// </summary>
    public int TargetNodeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string TargetNodeName { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public List<RockNodePathItemDto> Path { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public int? RootId { get; set; }
}