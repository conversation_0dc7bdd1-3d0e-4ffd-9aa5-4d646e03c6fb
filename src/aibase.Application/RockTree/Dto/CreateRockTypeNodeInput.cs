using System.ComponentModel.DataAnnotations;

// For RockNodeType if needed, though it's implicit

namespace aibase.RockTree.Dto;

/// <summary>
/// 
/// </summary>
public class CreateRockTypeNodeInput
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [StringLength(50)]
    public string? Code { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int RockTypeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ParentNodeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? RockTreeRootId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 
    /// </summary>
    [StringLength(7)]
    public string? DisplayColor { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [StringLength(2048)]
    public string? IconUrl { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? Order { get; set; }
}