using System;
using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using aibase.RockTypes.Dto;

namespace aibase.RockTree.Dto;

/// <inheritdoc />
[AutoMap(typeof(RockNode))]
public class RockNodeDto : FullAuditedEntityDto<int>
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public RockNodeType NodeType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? RockTypeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? DisplayColor { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? IconUrl { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public RockTypeDto? RockType { get; set; }
}