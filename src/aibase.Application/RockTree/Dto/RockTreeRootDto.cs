using Abp.Application.Services.Dto;
using Abp.AutoMapper;

namespace aibase.RockTree.Dto;

/// <inheritdoc />
[AutoMap(typeof(RockTreeRoot))]
public class RockTreeRootDto : FullAuditedEntityDto<int>
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? GeologySuiteId { get; set; } // Changed from Guid? to int?

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }
}