using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.MobileProfiles.Dto;

namespace aibase.MobileProfiles.Services;

/// <summary>
/// Mobile Profile service interface
/// </summary>
public interface IMobileProfileService : ITransientDependency
{
    /// <summary>
    /// Create a new mobile profile
    /// </summary>
    /// <param name="input">Mobile profile creation data</param>
    /// <param name="returnExist">Return existing if found</param>
    /// <returns>Created mobile profile</returns>
    Task<MobileProfile> CreateAsync(CreateMobileProfileDto input, bool returnExist = false);

    /// <summary>
    /// Get all mobile profiles with paging
    /// </summary>
    /// <param name="input">Paged request parameters</param>
    /// <returns>Paged result of mobile profiles</returns>
    Task<PagedResultDto<MobileProfileDto>> GetAllAsync(PagedMobileProfileResultRequestDto input);

    /// <summary>
    /// Update an existing mobile profile
    /// </summary>
    /// <param name="input">Mobile profile update data</param>
    /// <returns>Updated mobile profile</returns>
    Task<MobileProfileDto> UpdateAsync(UpdateMobileProfileDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<MobileProfileDto> GetAsync(EntityDto<int> input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<MobileProfileDto> GetMobileProfileByProjectAsync(int projectId);

    /// <summary>
    /// Delete a mobile profile
    /// </summary>
    /// <param name="id">Mobile profile ID</param>
    /// <returns>Task</returns>
    Task DeleteAsync(int id);
}