using System;
using System.IO;
using System.Threading.Tasks;
using Abp.BackgroundJobs;
using Abp.Dependency;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Runtime.Session;
using aibase.ImageEntity;
using aibase.Images.Dto;
using aibase.Images.Services.ProcessService;
using aibase.Images.Services.ResizeService;
using aibase.Jobs.Dto;
using aibase.Jobs.Services.Socket;
using aibase.WorkflowJobs;
using Hangfire;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace aibase.Jobs.Services.JobService
{
    /// <summary>
    /// 
    /// </summary>
    public class JobService : AsyncBackgroundJob<ProcessBatchDto>, ITransientDependency
    {
        private readonly IRepository<WorkflowJob, int> _workflowJobRepository;
        private readonly IRepository<FileEntity.File, int> _fileRepository;
        private readonly IRepository<WorkflowJobError, int> _workflowJobErrorRepository;
        private readonly IRepository<Image, int> _imageRepository;
        private readonly IHubContext<SocketWorkflowJob> _hubContext;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly IProcessService _processService;
        private readonly IResizeService _resizeService;
        private readonly IAbpSession _abpSession;

        /// <inheritdoc />
        public JobService(
            IRepository<FileEntity.File, int> fileRepository,
            IRepository<WorkflowJob, int> workflowJobRepository,
            IRepository<WorkflowJobError, int> workflowJobErrorRepository,
            IRepository<Image, int> imageRepository,
            IHubContext<SocketWorkflowJob> hubContext,
            IUnitOfWorkManager unitOfWorkManager,
            IProcessService processService,
            IResizeService resizeService,
            IAbpSession abpSession)
        {
            _fileRepository = fileRepository;
            _workflowJobRepository = workflowJobRepository;
            _workflowJobErrorRepository = workflowJobErrorRepository;
            _imageRepository = imageRepository;
            _hubContext = hubContext;
            _unitOfWorkManager = unitOfWorkManager;
            _processService = processService;
            _resizeService = resizeService;
            _abpSession = abpSession;
        }

        /// <inheritdoc />
        public override async Task ExecuteAsync(ProcessBatchDto input)
        {
            try
            {
                foreach (var image in input.Images)
                {
                    using (var transaction = _unitOfWorkManager.Begin()) // Start a new transaction for each image
                    {
                        var workflowJob = await _workflowJobRepository.GetAsync(input.WorkflowJobId);

                        var jobStorageConnection = JobStorage.Current.GetConnection();
                        var jobData = jobStorageConnection.GetJobData(workflowJob.JobId);

                        if (jobData.State != "Deleted" && workflowJob.TotalCompleted + workflowJob.TotalErrors <
                            workflowJob.TotalImage)
                        {
                            const int maxRetries = 1;
                            var currentRetry = 0;
                            var lastException = default(Exception);

                            while (currentRetry <= maxRetries)
                            {
                                try
                                {
                                    if (currentRetry > 0)
                                    {
                                        // Exponential backoff: 2^retry * 1000ms (1s, 2s, 4s)
                                        var delay = Math.Pow(2, currentRetry - 1) * 1000;
                                        await Task.Delay((int)delay);
                                        Logger.Info($"Retrying processing image {image.Id} (Attempt {currentRetry + 1}/{maxRetries + 1})");
                                    }

                                    await ProcessImageAsync(image.Id, workflowJob.WorkflowId, workflowJob.StatusDone);
                                    workflowJob.TotalCompleted += 1;
                                    Logger.Info("Successfully processed image: " + image.Id);
                                    break; // Success - exit the retry loop
                                }
                                catch (Exception ex)
                                {
                                    lastException = ex;
                                    Logger.Warn($"Processing image {image.Id} failed (Attempt {currentRetry + 1}/{maxRetries + 1}): {ex.Message}");
                                    
                                    if (currentRetry == maxRetries) // Last retry failed
                                    {
                                        workflowJob.TotalErrors += 1;

                                        // Store error details in database
                                        var imageEntity = await _imageRepository.GetAsync(image.Id);
                                        var workflowJobError = new WorkflowJobError
                                        {
                                            WorkflowJobId = workflowJob.Id,
                                            WorkflowJob = workflowJob,
                                            ImageId = image.Id,
                                            Image = imageEntity,
                                            ErrorMessage = $"Failed after {maxRetries + 1} attempts. Last error: {ex.Message}",
                                            StackTrace = ex.StackTrace ?? string.Empty,
                                            CreationTime = DateTime.Now,
                                            UserId = _abpSession.UserId,
                                            TenantId = _abpSession.TenantId
                                        };
                                        await _workflowJobErrorRepository.InsertAsync(workflowJobError);
                                        Logger.Error($"Processing image {image.Id} failed after {maxRetries + 1} attempts. Last error: {ex.Message}");
                                    }
                                }
                                currentRetry++;
                            }
                        }

                        // Check if all images have been processed
                        if (workflowJob.TotalCompleted + workflowJob.TotalErrors >= workflowJob.TotalImage)
                        {
                            workflowJob.Status = workflowJob.TotalErrors > 0 ? JobStatus.Failed : JobStatus.Completed;
                            BackgroundJob.Delete(workflowJob.JobId);
                        }

                        if (jobData.State == "Deleted")
                        {
                            if (workflowJob.TotalCompleted == workflowJob.TotalImage)
                            {
                                workflowJob.Status = JobStatus.Completed;
                            }

                            if (workflowJob.TotalErrors > 0)
                            {
                                workflowJob.Status = JobStatus.Failed;
                            }

                            if (workflowJob.TotalCompleted + workflowJob.TotalErrors < workflowJob.TotalImage)
                            {
                                workflowJob.Status = JobStatus.Canceled;
                            }
                        }

                        // Notify clients after processing each image
                        var jsonSerializerSettings = new JsonSerializerSettings
                        {
                            ContractResolver = new CamelCasePropertyNamesContractResolver()
                        };
                        await _hubContext.Clients.Group(workflowJob.Id.ToString())
                            .SendAsync("WorkflowJobSocket",
                                JsonConvert.SerializeObject(workflowJob, jsonSerializerSettings));

                        // Update the job after processing each image
                        await _workflowJobRepository.UpdateAsync(workflowJob);

                        await transaction.CompleteAsync(); // Commit transaction after each image
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Processing existingWorkflowJob {input.WorkflowJobId} failed: {ex.Message}");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tempFilePath"></param>
        /// <param name="fileNameUpload"></param>
        /// <param name="extension"></param>
        /// <param name="imageId"></param>
        /// <param name="workflowId"></param>
        public async Task ProcessResizedImage(string tempFilePath, string fileNameUpload, string? extension,
            int imageId, int? workflowId)
        {
            try
            {
                using (var fileStream = new FileStream(tempFilePath, FileMode.Open))
                {
                    var formFile = new FormFile(fileStream, 0, fileStream.Length, "", Path.GetFileName(tempFilePath))
                    {
                        Headers = new HeaderDictionary(),
                        ContentType = "application/octet-stream"
                    };

                    // Resize and upload the image to Azure
                    var resizedImage =
                        await _resizeService.ResizeAndUploadImageResizeAsync(formFile, fileNameUpload, extension);

                    using (var unitOfWork = _unitOfWorkManager.Begin())
                    {
                        // Insert the file entity into the database
                        var fileEntity = new FileEntity.File()
                        {
                            FileName = resizedImage.fileName,
                            Url = resizedImage.url,
                            Size = resizedImage.size,
                            Width = resizedImage.width,
                            Height = resizedImage.height,
                            ImageId = imageId
                        };

                        await _fileRepository.InsertAsync(fileEntity);
                        await _unitOfWorkManager.Current.SaveChangesAsync();

                        if (workflowId != null)
                        {
                            await ProcessImageAsync(imageId, workflowId.Value);
                        }

                        await unitOfWork.CompleteAsync();
                    }
                }
            }
            finally
            {
                // Delete the temporary file after processing
                if (File.Exists(tempFilePath))
                {
                    File.Delete(tempFilePath);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        public async Task ProcessImageAsync(int imageId, int workflowId, ImageStatus? imageStatus = null)
        {
            var processImageDto = new ProcessImageDto()
            {
                ImageId = imageId,
                WorkflowId = workflowId,
                ImageStatus = imageStatus
            };
            await _processService.ProcessImageV2(processImageDto);
        }
    }
}