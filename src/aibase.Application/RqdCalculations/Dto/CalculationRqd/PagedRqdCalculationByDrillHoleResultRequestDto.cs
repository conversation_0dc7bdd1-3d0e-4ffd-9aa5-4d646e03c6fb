using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;

namespace aibase.RqdCalculations.Dto.CalculationRqd;

/// <inheritdoc />
public class PagedRqdCalculationByDrillHoleResultRequestDto : PagedResultRequestDto
{
    
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int DrillHoleId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? RqdCalculationId { get; set; }
}