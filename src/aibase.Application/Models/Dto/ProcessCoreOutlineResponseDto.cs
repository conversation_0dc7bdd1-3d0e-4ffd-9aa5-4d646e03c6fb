using System.Collections.Generic;
using Newtonsoft.Json;

namespace aibase.Models.Dto;

/// <summary>
/// 
/// </summary>
public class ProcessCoreOutlineResponseDto
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("blocks")]
    public List<CoreOutlineBlockDto> Blocks { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("processing_info")]
    public CoreOutlineProcessingInfoDto ProcessingInfo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("warped_image_filename")]
    public string WarpedImageFilename { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("warped_image_url")]
    public string WarpedImageUrl { get; set; } = string.Empty;
}

/// <summary>
/// 
/// </summary>
public class CoreOutlineProcessingInfoDto
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("depth_range")]
    public CoreOutlineDepthRangeDto DepthRange { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("detection_time")]
    public double DetectionTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("gemini_processing_time")]
    public double GeminiProcessingTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("google_processing_time")]
    public double GoogleProcessingTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("total_blocks")]
    public int TotalBlocks { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("use_row_transformation")]
    public bool UseRowTransformation { get; set; }
}

/// <summary>
/// 
/// </summary>
public class CoreOutlineDepthRangeDto
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("from")]
    public double From { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("to")]
    public double To { get; set; }
}

/// <summary>
/// 
/// </summary>
public class CoreOutlineBlockDto
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("class")]
    public string Class { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("confidence")]
    public double Confidence { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("gemini_text")]
    public string GeminiText { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("google_text")]
    public string GoogleText { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("height")]
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("width")]
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("x")]
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("y")]
    public double Y { get; set; }
}